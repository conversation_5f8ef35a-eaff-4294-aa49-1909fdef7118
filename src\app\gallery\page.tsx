"use client";

import { useState, useEffect } from "react";
import { ImageGrid } from "@/components/gallery/image-grid";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Filter, 
  SortAsc, 
  SortDesc,
  RefreshCw,
  Plus
} from "lucide-react";
import { ProcessedImage } from "@/types";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";

type SortOption = "newest" | "oldest" | "status";
type FilterOption = "all" | "completed" | "processing" | "error";

export default function GalleryPage() {
  const [images, setImages] = useState<ProcessedImage[]>([]);
  const [filteredImages, setFilteredImages] = useState<ProcessedImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<SortOption>("newest");
  const [filterBy, setFilterBy] = useState<FilterOption>("all");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const { toast } = useToast();

  // Mock data for demonstration
  const mockImages: ProcessedImage[] = [
    {
      id: "1",
      originalUrl: "/api/placeholder/400/400",
      processedUrl: "/api/placeholder/400/400",
      backgroundRemovedUrl: "/api/placeholder/400/400",
      upscaledUrl: "/api/placeholder/400/400",
      finalUrl: "/api/placeholder/400/400",
      createdAt: new Date().toISOString(),
      status: "completed",
      steps: [],
    },
    {
      id: "2",
      originalUrl: "/api/placeholder/400/400",
      processedUrl: "/api/placeholder/400/400",
      backgroundRemovedUrl: "/api/placeholder/400/400",
      createdAt: new Date(Date.now() - 86400000).toISOString(),
      status: "processing",
      steps: [],
    },
    {
      id: "3",
      originalUrl: "/api/placeholder/400/400",
      processedUrl: "/api/placeholder/400/400",
      createdAt: new Date(Date.now() - 172800000).toISOString(),
      status: "error",
      steps: [],
    },
  ];

  const loadImages = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      // const response = await fetch("/api/gallery");
      // const data = await response.json();
      // setImages(data.images);
      
      // For now, use mock data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate loading
      setImages(mockImages);
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách hình ảnh",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadImages();
  }, []);

  useEffect(() => {
    let filtered = [...images];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(image => 
        image.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (filterBy !== "all") {
      filtered = filtered.filter(image => image.status === filterBy);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case "newest":
        case "oldest":
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          if (sortBy === "newest") comparison = -comparison;
          break;
        case "status":
          comparison = a.status.localeCompare(b.status);
          break;
      }
      
      return sortOrder === "desc" ? -comparison : comparison;
    });

    setFilteredImages(filtered);
  }, [images, searchTerm, filterBy, sortBy, sortOrder]);

  const handleImageClick = (image: ProcessedImage) => {
    // TODO: Open image detail modal or navigate to detail page
    console.log("Image clicked:", image);
  };

  const handleDelete = async (imageId: string) => {
    try {
      // TODO: Implement actual delete API call
      // await fetch(`/api/process/result/${imageId}`, { method: "DELETE" });
      
      setImages(prev => prev.filter(img => img.id !== imageId));
      toast({
        title: "Đã xóa",
        description: "Hình ảnh đã được xóa thành công",
      });
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể xóa hình ảnh",
        variant: "destructive",
      });
    }
  };

  const getStatusCount = (status: FilterOption) => {
    if (status === "all") return images.length;
    return images.filter(img => img.status === status).length;
  };

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Gallery</h1>
            <p className="text-muted-foreground">
              Quản lý và xem lại các hình ảnh đã xử lý
            </p>
          </div>
          <Button asChild>
            <Link href="/upload">
              <Plus className="mr-2 h-4 w-4" />
              Upload Mới
            </Link>
          </Button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{getStatusCount("all")}</div>
              <p className="text-sm text-muted-foreground">Tổng cộng</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{getStatusCount("completed")}</div>
              <p className="text-sm text-muted-foreground">Hoàn thành</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{getStatusCount("processing")}</div>
              <p className="text-sm text-muted-foreground">Đang xử lý</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{getStatusCount("error")}</div>
              <p className="text-sm text-muted-foreground">Lỗi</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Bộ Lọc & Tìm Kiếm
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm kiếm theo ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value as FilterOption)}
                  className="px-3 py-2 border border-input bg-background rounded-md text-sm"
                >
                  <option value="all">Tất cả trạng thái</option>
                  <option value="completed">Hoàn thành</option>
                  <option value="processing">Đang xử lý</option>
                  <option value="error">Lỗi</option>
                </select>
                
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as SortOption)}
                  className="px-3 py-2 border border-input bg-background rounded-md text-sm"
                >
                  <option value="newest">Mới nhất</option>
                  <option value="oldest">Cũ nhất</option>
                  <option value="status">Theo trạng thái</option>
                </select>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(prev => prev === "asc" ? "desc" : "asc")}
                >
                  {sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadImages}
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="flex gap-2 flex-wrap">
              <Badge variant="secondary">
                {filteredImages.length} kết quả
              </Badge>
              {searchTerm && (
                <Badge variant="outline">
                  Tìm kiếm: "{searchTerm}"
                </Badge>
              )}
              {filterBy !== "all" && (
                <Badge variant="outline">
                  Trạng thái: {filterBy}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Image Grid */}
        <ImageGrid
          images={filteredImages}
          onImageClick={handleImageClick}
          onDelete={handleDelete}
        />
      </div>
    </div>
  );
}

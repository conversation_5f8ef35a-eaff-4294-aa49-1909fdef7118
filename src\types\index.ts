// Image Processing Types
export interface ProcessingStep {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  message?: string;
}

export interface ImageUpload {
  id: string;
  file?: File;
  url?: string;
  preview: string;
  name: string;
  size: number;
  type: string;
}

export interface ProcessedImage {
  id: string;
  originalUrl: string;
  processedUrl: string;
  backgroundRemovedUrl?: string;
  upscaledUrl?: string;
  finalUrl?: string;
  createdAt: string;
  status: 'processing' | 'completed' | 'error';
  steps: ProcessingStep[];
}

// Runware API Types
export interface RunwareTask {
  taskType: 'removeBackground' | 'upscale' | 'imageInference';
  taskUUID?: string;
  inputImage?: string;
  outputFormat?: 'PNG' | 'JPEG' | 'WEBP';
  outputType?: 'base64Data' | 'URL';
}

export interface RunwareResponse {
  taskType: string;
  taskUUID: string;
  imageURL?: string;
  imageBase64?: string;
  error?: string;
}

// Supabase Types
export interface Database {
  public: {
    Tables: {
      processed_images: {
        Row: {
          id: string;
          original_url: string;
          processed_url: string | null;
          background_removed_url: string | null;
          upscaled_url: string | null;
          final_url: string | null;
          status: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          original_url: string;
          processed_url?: string | null;
          background_removed_url?: string | null;
          upscaled_url?: string | null;
          final_url?: string | null;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          original_url?: string;
          processed_url?: string | null;
          background_removed_url?: string | null;
          upscaled_url?: string | null;
          final_url?: string | null;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface UploadResponse {
  imageId: string;
  url: string;
  publicUrl: string;
}

export interface ProcessResponse {
  imageId: string;
  status: string;
  steps: ProcessingStep[];
}
